import {
  IChallengeDifficultyEnum,
  IChallengeDurationEnum,
} from './enums.challenge.interface';

export interface IExerciseAdditionalInfo {
  difficulty: IChallengeDifficultyEnum;
  durationType: IChallengeDurationEnum;
  duration?: number;
  loop?: number;
  points: number;
}

export interface ICreateBaseChallengeReq {
  // base exercise properties
  exerciseId: string;

  // additional
  additionalInfo: IExerciseAdditionalInfo;
}

export interface ICreateBaseChallenge {
  id: string;

  // base exercise properties
  name: string;
  description: string;
  mechanics: string;
  type: string;
  category: string[];
  forceType: string[];
  primaryMuscles: string[];
  secondaryMuscles: string[];
  equipments: string[];
  preview: string;

  // additional
  difficulty: IChallengeDifficultyEnum;
  duration?: number;
  loop?: number;
  points: number;
  startDate: Date;
  endDate: Date;
}

export interface ICreateBaseChallengeRes {
  id: string;

  // base exercise properties
  name: string;
  description: string;
  mechanics: string;
  type: string;
  category: string[];
  forceType: string[];
  primaryMuscles: string[];
  secondaryMuscles: string[];
  equipments: string[];
  preview: string;

  // additional
  difficulty: IChallengeDifficultyEnum;
  durationType?: IChallengeDurationEnum;
  duration?: number;
  loop?: number;
  points: number;
  startDate?: Date;
  endDate: Date;
  totalPick?: number;
  isActive?: boolean;
}

export interface IUpdateBaseChallengeReq {
  // base exercise properties
  name: string;
  description: string;
  mechanics: string;
  type: string;
  category: string[];
  forceType: string[];
  primaryMuscles: string[];
  secondaryMuscles: string[];
  equipments: string[];
  preview: string;

  // additional
  difficulty: IChallengeDifficultyEnum;
  duration: number;
  loop: number;
  points: number;
}
