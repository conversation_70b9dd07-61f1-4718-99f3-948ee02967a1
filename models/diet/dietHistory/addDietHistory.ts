import { SuccessResponse } from '../../common/successResponse';
import { DietHistory, FoodSource, MealType } from './dietHistory';

export interface CreateDietHistoryRequestBody {
  mealType: MealType;
  date: Date;
  userId?: string;
  foodId: string;
  servingSize: number;
  source: FoodSource;
}

export interface CreateAiDietHistoryRequestBody {
  mealType: MealType;
  date: Date;
  userId?: string;
  // foodId?: string;
  name: string;
  knownAs?: string;
  calories: string;
  carb: string;
  protein: string;
  fat: string;
  servingSize: number;
  source: FoodSource;
}

export interface CreateDietHistorySuccessResponse extends SuccessResponse {
  data: DietHistory;
}

export const enum CreateDietHistoryErrorMessage {
  CAN_NOT_ADD_CONSUMED_FOOD = 'Can not add consumed food',
  CAN_NOT_ADD_WATER = 'Can not add water',
  FOOD_NOT_EXISTS = 'Food does not exist',
}
