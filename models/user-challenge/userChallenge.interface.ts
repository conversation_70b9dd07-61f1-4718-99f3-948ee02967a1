import { IUserChallengeStatusEnum } from 'models/challenge';

export interface ICreateUserChallengeReq {
  challengeId: string;
}
export interface ICreateUserChallenge {
  userId: string;
  challengeId: string;
  points: number;
  expireAt: Date;
  challengeName?: string;
  durationType?: string;
}

export interface ICreatedUserChallengeDetails {
  name: string;
  duration: number;
  loop: number;
  points: number;
  preview: string;
}
export interface ICreateUserChallengeRes {
  id: string;
  userId: string;
  challengeId: string;
  lastStatus: IUserChallengeStatusEnum;
  expireAt: Date;
  challengeDetails?: ICreatedUserChallengeDetails;
}
