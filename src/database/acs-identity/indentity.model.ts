import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { ACSIdentity } from 'src/modules/coach/acs-meeting/entities/identity.entity';

const ACSIdentitySchema = new Schema<ACSIdentity>(
  {
    id: {
      type: String,
      default: randomUUID(),
    },
    userId: {
      type: String,
      default: null,
    },
    acsId: {
      type: String,
      default: null,
    },
    communicationUserId: {
      type: String,
      default: null,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);


const ACSIdentityModel = model<ACSIdentity>('acsIdentity', ACSIdentitySchema);
export { ACSIdentityModel };
