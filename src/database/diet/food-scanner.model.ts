import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { FoodRecognitionResult, FoodScannerEntity } from 'src/modules/diet/food-scanner-ai/entities/scanner.entitiy';


const foodInfoSchema = new Schema<FoodRecognitionResult>(
  {
    name: {
      type: String,
      default: null,
    },
    servingSize: {
      type: Number,
      default: null,
    },
    calories: {
      type: Number,
      default: null,
    },
    carb: {
      type: Number,
      default: null,
    },
    fat: {
      type: Number,
      default: null,
    },
    protein: {
      type: Number,
      default: null,
    },
    confidence: {
      type: Number,
      default: null,
    },
    foodItems: {
      type: [String],
      default: null,
    },
  },
  {
    _id: false,
    versionKey: false,
    timestamps: true,
  }
);


const foodScannerSchema = new Schema<FoodScannerEntity>(
  {
    id: {
      type:String,
      default: () => randomUUID(),
    },
    userId: {
      type: String,
      default: null,
    },
    imageUrl: {
        type: String,
        default: null,
    },
    foodInfo: {
        type: [foodInfoSchema],
        default: null,
    },
    isFoodDetected: {
        type: Boolean,
        default: true,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);


const FoodScannerEntityModel = model<FoodScannerEntity>(
  'food-scanner',
  foodScannerSchema,
);
export { FoodScannerEntityModel };
