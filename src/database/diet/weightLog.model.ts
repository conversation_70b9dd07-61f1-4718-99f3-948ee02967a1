import { randomUUID } from 'crypto';
import { Schema, model } from 'mongoose';
import { WeightLog } from 'src/entity/globalFood';

const weightLogSchema = new Schema<WeightLog>({
  id: { type: String, default: () => randomUUID(), unique: true },
  userId: { type: String, required: true },
  weight: { type: Number, required: true },
  weightType: { type: String, enum: ['kg', 'lb'], default: 'kg' },
  date: { type: Date, required: true },
  notes: { type: String, default: '' },
  createdAt: { type: Date, default: Date.now },
});

weightLogSchema.index({ userId: 1, date: -1 });

export const WeightLogModel = model('weight-log', weightLogSchema);
