import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { Food } from 'src/entity/food';

const foodSchema = new Schema<Food>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
    },
    foodNameEnglish: {
      type: String,
      required: true,
    },
    foodNameBengali: {
      type: String,
      required: true,
    },
    image: {
      type: String,
      required: false,
    },
    calories: {
      type: String,
      required: true,
    },
    protein: {
      type: String,
      required: true,
    },
    fat: {
      type: String,
      required: true,
    },
    carb: {
      type: String,
      required: true,
    },
    minerals: {
      ca: { type: String, required: false },
      fe: { type: String, required: false },
      mg: { type: String, required: false },
      p: { type: String, required: false },
      k: { type: String, required: false },
      na: { type: String, required: false },
      zn: { type: String, required: false },
      cu: { type: String, required: false },
    },
    vitamins: {
      vitaminA: { type: String, required: false },
      retinol: { type: String, required: false },
      betaCarotene: { type: String, required: false },
      vitaminD: { type: String, required: false },
      vitaminE: { type: String, required: false },
      thiamin: { type: String, required: false },
      riboflavin: { type: String, required: false },
      niacin: { type: String, required: false },
      vitaminB6: { type: String, required: false },
      folate: { type: String, required: false },
      vitaminC: { type: String, required: false },
    },
    isDeleted: {
      type: Boolean,
      required: true,
      default: false,
    },
  },
  {
    versionKey: false,
    timestamps: true,
  },
);

const FoodModel = model<Food>('food', foodSchema);
export { FoodModel };
