import { Injectable, Logger } from '@nestjs/common';
import type { ChatCompletionMessageParam } from 'openai/resources/chat/completions';
import { Observable } from 'rxjs';
import { AzureOpenAIService } from '../../services/azure-openai.service';

export interface ChatMessage {
  role: string;
  content: string;
  createdAt?: Date;
}

export interface ChatSession {
  id?: string;
  userId: string;
  messages: ChatMessage[];
}

@Injectable()
export class ChatbotService {
  private readonly logger = new Logger(ChatbotService.name);
  private readonly systemPrompt = `You are a chatbot named "Motivator" for a fitness app designed for average Bengali households. Respond to user queries related to general health, fitness, and diet. Your responses maintain a professional tone.

  Here's how you should operate:

  Answer General Questions: Respond accurately and concisely to user questions about general health, fitness, sports, exercise, and diet. Give detailed answers. Provide accurate, concise, and personalized advice on:
  - Diet and nutrition
  - Exercise and workout routines
  - Weight management
  - Mental health and stress management (e.g., anxiety, depression, stress relief techniques, mindfulness,  ADHD)
  - Sports and games
  - General health tips

  Diet Chart Generation: If a user requests a diet plan, follow these steps:
  1. First answer the greetings like hello, hi etc.

  2. Language Detection and Response: Detect the language of the user's query. Respond in the same language as the query.

  3. Ask for the required information step by step:
      - Ask one question at a response, starting with height, weight, age, gender, activity level, dietary preferences, and specific goals (e.g., weight loss, muscle gain).
      - Wait for the user's response before proceeding to the next question.
  4. Generate a basic diet chart suitable for a Bangladeshi household, including proper caloric counting based on the user's information. The chart should include Bengali food names and be realistic for a typical Bangladeshi household.

  5. Provide estimated calorie counts for each meal and the total daily intake with details.

  Limitations:
  Scope: Only answer questions directly related to health, fitness, diet, sports, and exercise.
  Vocabulary: Avoid using the word "নমস্কার" in your responses.
  Off-topic Responses: If a query is not related to greetings, health, fitness, diet, sports, and exercise, respond with: "দুঃখিত আমি একজন ফিটনেস চ্যাটবট, আমি শুধু স্বাস্থ্য, ফিটনেস, ডায়েট, খেলাধুলা এবং মানসিক স্বাস্থ্য সম্পর্কিত প্রশ্নের উত্তর দেই।" for Bengali input. For English input respond with: "I'm sorry, I can only answer questions related to health, fitness, nutrition, sports and wellness."`;

  constructor(private azureOpenAIService: AzureOpenAIService) {}

  /**
   * Generate a chat response
   */
  async generateChatResponse(
    query: string,
    history: ChatMessage[] = [],
    userInfo?: { height?: number; weight?: number; age?: number; gender?: string }
  ): Promise<string> {
    try {
      const messages = this.formatMessagesForAPI(query, history, userInfo);
      
      return await this.azureOpenAIService.generateChatCompletion(messages, {
        temperature: 0.7,
        max_tokens: 1000,
      });
    } catch (error) {
      this.logger.error('Error generating chat response:', error);
      throw error;
    }
  }

  /**
   * Stream a chat response
   */
  async streamChatResponse(
    query: string,
    history: ChatMessage[] = [],
    userInfo?: { height?: number; weight?: number; age?: number; gender?: string }
  ): Promise<Observable<string>> {
    try {
      const messages = this.formatMessagesForAPI(query, history, userInfo);
      
      const responseStream = await this.azureOpenAIService.streamChatCompletion(messages, {
        temperature: 0.7,
        max_tokens: 1000,
      });

      return new Observable<string>((observer) => {
        responseStream.on('data', (chunk) => observer.next(chunk.toString()));
        responseStream.on('end', () => observer.complete());
        responseStream.on('error', (error) => observer.error(error));
      });
    } catch (error) {
      this.logger.error('Error streaming chat response:', error);
      throw error;
    }
  }

  /**
   * Format messages for the API
   */
  private formatMessagesForAPI(
    query: string,
    history: ChatMessage[] = [],
    userInfo?: { height?: number; weight?: number; age?: number; gender?: string }
  ): ChatCompletionMessageParam[] {
    const messages: ChatCompletionMessageParam[] = [
      {
        role: 'system',
        content: this.systemPrompt,
      },
    ];

    // Add history messages (limited to last 20 for context window management)
    const recentHistory = history.slice(-20);
    recentHistory.forEach((msg) => {
      messages.push({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content,
      });
    });

    // Add user info if available
    let enhancedQuery = query;
    if (userInfo) {
      const userInfoString = `User info: height = ${userInfo.height || 'N/A'}, weight = ${userInfo.weight || 'N/A'}, age = ${userInfo.age || 'N/A'}, gender = ${userInfo.gender || 'N/A'}`;
      enhancedQuery = `${query}. ${userInfoString}`;
    }

    // Add the current query
    messages.push({
      role: 'user',
      content: enhancedQuery,
    });

    return messages;
  }
}
