export interface AITextRequest {
  prompt: string;
  temperature?: number;
  maxTokens?: number;
  userId?: string;
}

export interface AIChatRequest {
  messages: Array<{
    role: string;
    content: string;
  }>;
  temperature?: number;
  maxTokens?: number;
  userId?: string;
  stream?: boolean;
}

export interface AIImageAnalysisRequest {
  imageData: Buffer;
  prompt: string;
  temperature?: number;
  maxTokens?: number;
  responseFormat?: 'text' | 'json_object';
  userId?: string;
}
