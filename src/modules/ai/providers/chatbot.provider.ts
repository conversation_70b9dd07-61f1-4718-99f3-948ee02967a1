import { Injectable, Logger } from "@nestjs/common";
import { ChatCompletionMessageParam } from "openai/resources/chat/completions";
import { Readable } from "stream";
import { AzureOpenAIService } from "../services/azure-openai.service";

export interface ChatMessage {
  role: string;
  content: string;
  createdAt?: Date;
}

@Injectable()
export class ChatbotProvider {
  private readonly logger = new Logger(ChatbotProvider.name);
  constructor(private readonly azureOpenAIService: AzureOpenAIService) { }

  /**
   * Generate a chat completion response
   */
  async generateChatCompletion(messages: ChatCompletionMessageParam[]): Promise<string> {
    try {
      return await this.azureOpenAIService.generateChatCompletion(messages, {
        temperature: 0.7,
        max_tokens: 1000,
      });
    } catch (error) {
      this.logger.error('Error generating chat completion:', error);
      throw error;
    }
  }

  /**
   * Stream a chat completion response
   */
  async streamChatCompletion(messages: ChatCompletionMessageParam[]): Promise<Readable> {
    try {
      return await this.azureOpenAIService.streamChatCompletion(messages, {
        temperature: 0.7,
        max_tokens: 1000,
      });
    } catch (error) {
      this.logger.error('Error streaming chat completion:', error);
      throw error;
    }
  }


}
