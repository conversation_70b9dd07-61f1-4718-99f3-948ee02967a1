import { Injectable, Logger } from "@nestjs/common";
import { FoodRecognitionResult } from "src/modules/diet/food-scanner-ai/entities/scanner.entitiy";
import { AzureOpenAIService, ImageAnalysisResponse, ScannerInput } from "../services/azure-openai.service";

@Injectable()
export class FoodScannerAIProvider {
  private readonly logger = new Logger(FoodScannerAIProvider.name);

  constructor(private readonly azureOpenAIService: AzureOpenAIService) { }

  async analyzeFoodImage(request: ScannerInput): Promise<{ items: FoodRecognitionResult[], isFoodDetected: boolean }> {
    const response: ImageAnalysisResponse = await this.azureOpenAIService.analyzeImage(request);
    
    if (!response || !response.content) {
      throw new Error('No response from AI service');
    }
    
    try {
      const parsedResult = JSON.parse(response.content);
      const isFoodDetected = parsedResult.isFoodDetected === true;
      
      // Handle single item response format
      const items: FoodRecognitionResult[] = [];
      if (isFoodDetected && parsedResult.item) {
        items.push(this.validateAndFormatResult(parsedResult.item));
      }
      
      return {
        items,
        isFoodDetected
      };
    } catch (error) {
      this.logger.error('Error parsing AI response:', error);
      throw new Error('Failed to parse food recognition data from AI response');
    }
  }

  /**
   * Validate and format the AI response
   */
  private validateAndFormatResult(result: any): FoodRecognitionResult {
    const ingredients = Array.isArray(result.foodItems)
      ? result.foodItems.filter((x: any) => typeof x === 'string')
      : [];

    const confidence = Number(result.confidence);
    const clampedConfidence = isFinite(confidence)
      ? Math.max(0, Math.min(1, confidence))
      : 0.5;
    const knownAs = typeof result.knownAs === 'string' ? result.knownAs : undefined;
    const canonicalName = result.name || knownAs || 'Unknown Food';

    return {
      name: canonicalName,
      knownAs,
      servingSize: Number(result.servingSize) || 1,
      calories: Number(result.calories) || 0,
      carb: Number(result.carb) || 0,
      fat: Number(result.fat) || 0,
      protein: Number(result.protein) || 0,
      confidence: clampedConfidence,
      foodItems: ingredients,
    };
  }
}
