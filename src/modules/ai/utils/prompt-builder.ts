export class PromptBuilder {
  private prompt = '';

  constructor(initialPrompt?: string) {
    if (initialPrompt) {
      this.prompt = initialPrompt;
    }
  }

  /**
   * Add a system instruction
   */
  addSystemInstruction(instruction: string): PromptBuilder {
    this.prompt += `System: ${instruction}\n\n`;
    return this;
  }

  /**
   * Add user context
   */
  addUserContext(context: string): PromptBuilder {
    this.prompt += `Context: ${context}\n\n`;
    return this;
  }

  /**
   * Add a specific instruction
   */
  addInstruction(instruction: string): PromptBuilder {
    this.prompt += `Instruction: ${instruction}\n\n`;
    return this;
  }

  /**
   * Add examples
   */
  addExamples(examples: Array<{ input: string; output: string }>): PromptBuilder {
    this.prompt += 'Examples:\n';
    examples.forEach((example, index) => {
      this.prompt += `${index + 1}. Input: ${example.input}\n   Output: ${example.output}\n`;
    });
    this.prompt += '\n';
    return this;
  }

  /**
   * Add constraints
   */
  addConstraints(constraints: string[]): PromptBuilder {
    this.prompt += 'Constraints:\n';
    constraints.forEach((constraint, index) => {
      this.prompt += `${index + 1}. ${constraint}\n`;
    });
    this.prompt += '\n';
    return this;
  }

  /**
   * Add the actual query/request
   */
  addQuery(query: string): PromptBuilder {
    this.prompt += `Query: ${query}\n\n`;
    return this;
  }

  /**
   * Add output format specification
   */
  addOutputFormat(format: string): PromptBuilder {
    this.prompt += `Output Format: ${format}\n\n`;
    return this;
  }

  /**
   * Build and return the final prompt
   */
  build(): string {
    return this.prompt.trim();
  }

  /**
   * Clear the current prompt
   */
  clear(): PromptBuilder {
    this.prompt = '';
    return this;
  }

  /**
   * Static method to create a fitness-specific prompt
   */
  static createFitnessPrompt(query: string, userInfo?: {
    height?: number;
    weight?: number;
    age?: number;
    gender?: string;
    activityLevel?: string;
    goals?: string[];
  }): string {
    const builder = new PromptBuilder();
    
    builder
      .addSystemInstruction('You are a professional fitness and nutrition advisor for Bengali households.')
      .addInstruction('Provide accurate, culturally appropriate advice for fitness, nutrition, and health.')
      .addConstraints([
        'Only answer questions related to health, fitness, nutrition, sports, and wellness',
        'Provide Bengali food names when suggesting diet plans',
        'Consider typical Bengali household ingredients and cooking methods',
        'Respond in the same language as the query (Bengali or English)'
      ]);

    if (userInfo) {
      const contextParts = [];
      if (userInfo.height) contextParts.push(`Height: ${userInfo.height}cm`);
      if (userInfo.weight) contextParts.push(`Weight: ${userInfo.weight}kg`);
      if (userInfo.age) contextParts.push(`Age: ${userInfo.age} years`);
      if (userInfo.gender) contextParts.push(`Gender: ${userInfo.gender}`);
      if (userInfo.activityLevel) contextParts.push(`Activity Level: ${userInfo.activityLevel}`);
      if (userInfo.goals) contextParts.push(`Goals: ${userInfo.goals.join(', ')}`);
      
      if (contextParts.length > 0) {
        builder.addUserContext(contextParts.join(', '));
      }
    }

    return builder.addQuery(query).build();
  }

  /**
   * Static method to create a food analysis prompt
   */
  static createFoodAnalysisPrompt(additionalInstructions?: string): string {
    const builder = new PromptBuilder();
    
    return builder
      .addSystemInstruction('You are a professional nutritionist analyzing food images.')
      .addInstruction('Analyze the food image and provide detailed nutritional information.')
      .addConstraints([
        'Be as accurate as possible based on visual analysis',
        'Focus on Bengali/South Asian cuisine when applicable',
        'Provide realistic portion sizes',
        'Include confidence scores for your analysis'
      ])
      .addOutputFormat('JSON object with foodName, calories, protein, carbs, fat, fiber, servingSize, confidence, ingredients, and allergens')
      .addQuery(additionalInstructions || 'Analyze this food image and provide nutritional information.')
      .build();
  }
}
