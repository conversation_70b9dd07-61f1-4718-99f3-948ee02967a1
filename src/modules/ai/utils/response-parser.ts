export class ResponseParser {
  /**
   * Parse JSON response with error handling
   */
  static parseJSON<T>(jsonString: string): T | null {
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.error('Failed to parse JSON response:', error);
      return null;
    }
  }

  /**
   * Extract JSON from mixed content response
   */
  static extractJSON(content: string): any | null {
    try {
      // Try to find JSON block in the response
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/) || 
                       content.match(/\{[\s\S]*\}/) ||
                       content.match(/\[[\s\S]*\]/);
      
      if (jsonMatch) {
        return JSON.parse(jsonMatch[1] || jsonMatch[0]);
      }
      
      // If no JSON block found, try parsing the entire content
      return JSON.parse(content);
    } catch (error) {
      console.error('Failed to extract <PERSON><PERSON><PERSON> from response:', error);
      return null;
    }
  }

  /**
   * Clean and format text response
   */
  static cleanTextResponse(content: string): string {
    return content
      .trim()
      .replace(/\n\s*\n/g, '\n\n') // Remove excessive line breaks
      .replace(/^\s+|\s+$/gm, '') // Remove leading/trailing whitespace from lines
      .replace(/\*\*(.*?)\*\*/g, '$1') // Remove markdown bold formatting
      .replace(/\*(.*?)\*/g, '$1'); // Remove markdown italic formatting
  }

  /**
   * Parse nutritional information from text
   */
  static parseNutritionalInfo(content: string): {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    fiber?: number;
  } {
    const nutritionInfo: any = {};
    
    // Extract calories
    const caloriesMatch = content.match(/calories?:?\s*(\d+(?:\.\d+)?)/i);
    if (caloriesMatch) {
      nutritionInfo.calories = parseFloat(caloriesMatch[1]);
    }

    // Extract protein
    const proteinMatch = content.match(/protein:?\s*(\d+(?:\.\d+)?)/i);
    if (proteinMatch) {
      nutritionInfo.protein = parseFloat(proteinMatch[1]);
    }

    // Extract carbs
    const carbsMatch = content.match(/carb(?:ohydrate)?s?:?\s*(\d+(?:\.\d+)?)/i);
    if (carbsMatch) {
      nutritionInfo.carbs = parseFloat(carbsMatch[1]);
    }

    // Extract fat
    const fatMatch = content.match(/fat:?\s*(\d+(?:\.\d+)?)/i);
    if (fatMatch) {
      nutritionInfo.fat = parseFloat(fatMatch[1]);
    }

    // Extract fiber
    const fiberMatch = content.match(/fiber:?\s*(\d+(?:\.\d+)?)/i);
    if (fiberMatch) {
      nutritionInfo.fiber = parseFloat(fiberMatch[1]);
    }

    return nutritionInfo;
  }

  /**
   * Validate and sanitize AI response
   */
  static validateResponse(content: string, maxLength = 5000): string {
    if (!content || typeof content !== 'string') {
      throw new Error('Invalid response content');
    }

    if (content.length > maxLength) {
      console.warn(`Response truncated from ${content.length} to ${maxLength} characters`);
      return content.substring(0, maxLength) + '...';
    }

    return content;
  }

  /**
   * Extract confidence score from response
   */
  static extractConfidence(content: string): number {
    const confidenceMatch = content.match(/confidence:?\s*(\d+(?:\.\d+)?)/i) ||
                           content.match(/accuracy:?\s*(\d+(?:\.\d+)?)/i);
    
    if (confidenceMatch) {
      const confidence = parseFloat(confidenceMatch[1]);
      // Normalize to 0-1 range if it's in percentage
      return confidence > 1 ? confidence / 100 : confidence;
    }
    
    return 0.5; // Default confidence
  }

  /**
   * Parse list items from response
   */
  static parseList(content: string): string[] {
    const lines = content.split('\n');
    const listItems: string[] = [];
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      // Match numbered lists, bullet points, or dash lists
      const listMatch = trimmedLine.match(/^(?:\d+\.|\*|-)\s*(.+)$/);
      if (listMatch) {
        listItems.push(listMatch[1].trim());
      }
    }
    
    return listItems;
  }

  /**
   * Extract key-value pairs from response
   */
  static extractKeyValuePairs(content: string): Record<string, string> {
    const pairs: Record<string, string> = {};
    const lines = content.split('\n');
    
    for (const line of lines) {
      const match = line.match(/^(.+?):\s*(.+)$/);
      if (match) {
        const key = match[1].trim().toLowerCase();
        const value = match[2].trim();
        pairs[key] = value;
      }
    }
    
    return pairs;
  }
}
