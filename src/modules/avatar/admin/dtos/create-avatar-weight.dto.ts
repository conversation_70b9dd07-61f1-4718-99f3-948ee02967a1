import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsNumber, IsObject } from 'class-validator';
import { BodyStatus } from '../../common/const/enum';

export class CreateWeightThresholdDto {
  @ApiProperty({ enum: BodyStatus })
  @IsEnum(BodyStatus)
  bodyStatus: BodyStatus;

  @ApiProperty()
  @IsNumber()
  min: number;

  @ApiProperty()
  @IsNumber()
  max: number;
}

export class CreateWeightThresholdResponseDto {
  @Expose()
  @ApiProperty({ description: 'Unique identifier for the weight threshold' })
  id: string;

  @Expose()
  @ApiProperty({ description: 'Body status', enum: BodyStatus })
  bodyStatus: BodyStatus;

  @Expose()
  @ApiProperty({ description: 'Minimum weight threshold' })
  min: number;

  @Expose()
  @ApiProperty({ description: 'Maximum weight threshold' })
  max: number;
}

export class CreateWeightThresholdSuccessResponseDto {
  @Expose()
  @ApiProperty({ description: 'Details of the created weight threshold' })
  @IsObject()
  @IsNotEmpty()
  data: CreateWeightThresholdResponseDto;
}
