import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsObject, IsString } from 'class-validator';
import { AvatarGender, AvatarStyle, AvatarType } from '../../common/const/enum';

export class CreateAvatarDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ enum: AvatarGender })
  @IsEnum(AvatarGender)
  gender: AvatarGender;

  @ApiProperty({ enum: AvatarStyle })
  @IsEnum(AvatarStyle)
  style: AvatarStyle;

  @ApiProperty({ enum: AvatarType })
  @IsEnum(AvatarType)
  type: AvatarType;

  @ApiProperty()
  @IsString()
  thumbnail: string;
}

export class CreateAvatarResponseDto {
  @Expose()
  @ApiProperty({ description: 'Unique identifier for the avatar' })
  id: string;

  @Expose()
  @ApiProperty({ description: 'Name of the avatar' })
  name: string;

  @Expose()
  @ApiProperty({ description: 'Gender of the avatar', enum: AvatarGender })
  gender: AvatarGender;

  @Expose()
  @ApiProperty({ description: 'Style of the avatar', enum: AvatarStyle })
  style: AvatarStyle;

  @Expose()
  @ApiProperty({ description: 'Type of the avatar', enum: AvatarType })
  type: AvatarType;

  @Expose()
  @ApiProperty({ description: 'Thumbnail image URL of the avatar' })
  thumbnail: string;
}

export class CreateAvatarSuccessResponseDto {
  @Expose()
  @ApiProperty({ description: 'Details of the created avatar' })
  @IsObject()
  @IsNotEmpty()
  data: CreateAvatarResponseDto;
}
