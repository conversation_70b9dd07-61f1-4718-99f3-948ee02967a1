import { Injectable } from '@nestjs/common';
import { AvatarType } from '../../common/const/enum';
import { AccessoryModel } from '../../common/db/accessory.model';
import { AvatarBaseBodyModel } from '../../common/db/avatar-base-body.model';
import { AvatarEmotionAssetModel } from '../../common/db/avatar-emotion-asset.model';
import { AvatarEmotionModel } from '../../common/db/avatar-emotion.model';
import { AvatarWeightThresholdModel } from '../../common/db/avatar-weight.model';
import { AvatarModel } from '../../common/db/avatar.model';
import { Accessory } from '../../common/entities/accessory';
import { Avatar } from '../../common/entities/avatar';
import { AvatarBaseBody } from '../../common/entities/avatar-base-body';
import { AvatarEmotionSupport } from '../../common/entities/avatar-emotion';
import { AvatarEmotionAsset } from '../../common/entities/avatar-emotion-asset';
import { AvatarWeightThreshold } from '../../common/entities/avatar-weight';

@Injectable()
export class AvatarRepository {
  // ...existing code...

  // Admin Avatar Management
  async createAvatar(avatarData: Avatar): Promise<Avatar | null> {
    try {
      const avatar = await AvatarModel.create(avatarData);
      return avatar?.toObject();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async findAllActiveAvatars(): Promise<Avatar[]> {
    try {
      return await AvatarModel.find({ isActive: true }).lean();
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }

  async findAvatarById(avatarId: string): Promise<Avatar | null> {
    try {
      return await AvatarModel.findOne({ id: avatarId }).select('-_id').lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  // Weight Thresholds
  async createWeightThreshold(
    thresholdData: AvatarWeightThreshold,
  ): Promise<AvatarWeightThreshold | null> {
    try {
      const threshold = await AvatarWeightThresholdModel.create(thresholdData);
      return threshold?.toObject();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async findWeightThresholdsByAvatarId(
    avatarId: string,
  ): Promise<AvatarWeightThreshold[]> {
    try {
      return await AvatarWeightThresholdModel.find({ avatarId })
        .select('-_id')
        .lean();
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }

  // Base Bodies
  async createBaseBody(
    baseBodyData: AvatarBaseBody,
  ): Promise<AvatarBaseBody | null> {
    try {
      const baseBody = await AvatarBaseBodyModel.create(baseBodyData);
      return baseBody?.toObject();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async findBaseBodyByAvatarId(avatarId: string): Promise<AvatarBaseBody[]> {
    try {
      return await AvatarBaseBodyModel.find({ avatarId }).select('-_id').lean();
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }

  // Emotions
  async createEmotion(
    emotionData: AvatarEmotionSupport,
  ): Promise<AvatarEmotionSupport | null> {
    try {
      const emotion = await AvatarEmotionModel.create(emotionData);
      return emotion?.toObject();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async findEmotionsByAvatarId(
    avatarId: string,
  ): Promise<AvatarEmotionSupport[]> {
    try {
      return await AvatarEmotionModel.find({ avatarId }).select('-_id').lean();
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }

  // Assets
  async createEmotionAsset(
    assetData: AvatarEmotionAsset,
  ): Promise<AvatarEmotionAsset | null> {
    try {
      const asset = await AvatarEmotionAssetModel.create(assetData);
      return asset?.toObject();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async findEmotionAssetsByAvatarId(
    avatarId: string,
  ): Promise<AvatarEmotionAsset[]> {
    try {
      return await AvatarEmotionAssetModel.find({ avatarId })
        .select('-_id')
        .lean();
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }

  async findEmotionAssetByConditions(
    avatarId: string,
    type: AvatarType,
    bodyStatus: string,
    emotion: string,
  ): Promise<AvatarEmotionAsset | null> {
    try {
      return await AvatarEmotionAssetModel.findOne({
        avatarId,
        type,
        bodyStatus,
        emotion,
      }).lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  // Accessories
  async createAccessory(accessoryData: Accessory): Promise<Accessory | null> {
    try {
      const accessory = await AccessoryModel.create(accessoryData);
      return accessory?.toObject();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  // Helper method for calculating body status
  async calculateBodyStatusFromWeight(
    avatarId: string,
    weight: number,
  ): Promise<string> {
    try {
      const thresholds = await this.findWeightThresholdsByAvatarId(avatarId);

      for (const threshold of thresholds) {
        if (weight >= threshold.min && weight <= threshold.max) {
          return threshold.bodyStatus;
        }
      }

      return 'normal'; // default
    } catch (error) {
      console.log(error.message);
      return 'normal';
    }
  }

  async updateBaseBody(
    baseBodyId: string,
    updateData: Partial<AvatarBaseBody>,
  ): Promise<AvatarBaseBody | null> {
    try {
      const updated = await AvatarBaseBodyModel.findOneAndUpdate(
        { id: baseBodyId },
        updateData,
        { new: true },
      ).lean();
      return updated;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async deleteBaseBody(baseBodyId: string): Promise<boolean> {
    try {
      const result = await AvatarBaseBodyModel.deleteOne({ id: baseBodyId });
      return result.deletedCount > 0;
    } catch (error) {
      console.log(error.message);
      return false;
    }
  }

  async findBaseBodyById(baseBodyId: string): Promise<AvatarBaseBody | null> {
    try {
      return await AvatarBaseBodyModel.findOne({ id: baseBodyId })
        .select('-_id')
        .lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async findEmotionById(
    emotionId: string,
  ): Promise<AvatarEmotionSupport | null> {
    try {
      return await AvatarEmotionModel.findOne({ id: emotionId })
        .select('-_id')
        .lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async findEmotion(emotion: string): Promise<AvatarEmotionSupport | null> {
    try {
      return await AvatarEmotionModel.findOne({ emotion })
        .select('-_id')
        .lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }
  async updateEmotion(
    emotionId: string,
    updateData: Partial<AvatarEmotionSupport>,
  ): Promise<AvatarEmotionSupport | null> {
    try {
      const updated = await AvatarEmotionModel.findOneAndUpdate(
        { id: emotionId },
        { ...updateData, updatedAt: new Date() },
        { new: true },
      )
        .select('-_id')
        .lean();
      return updated;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async deleteEmotion(emotionId: string): Promise<any> {
    try {
      const result = await AvatarEmotionModel.deleteOne({ id: emotionId });
      return result.deletedCount > 0;
    } catch (error) {
      console.log(error.message);
      return false;
    }
  }

  async checkEmotionExists(
    avatarId: string,
    emotion: string,
  ): Promise<boolean> {
    try {
      const emotionExists = await AvatarEmotionModel.findOne({
        avatarId,
        emotion,
      });
      return !!emotionExists;
    } catch (error) {
      console.log(error.message);
      return false;
    }
  }

  // ...existing code...

  async findEmotionAssetsByEmotionId(
    emotionId: string,
  ): Promise<AvatarEmotionAsset[]> {
    try {
      return await AvatarEmotionAssetModel.find({ emotionId })
        .select('-_id')
        .lean();
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }

  async findAllEmotionAssets(): Promise<AvatarEmotionAsset[]> {
    try {
      return await AvatarEmotionAssetModel.find().select('-_id').lean();
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }

  async findEmotionAssetById(
    assetId: string,
  ): Promise<AvatarEmotionAsset | null> {
    try {
      return await AvatarEmotionAssetModel.findOne({ id: assetId })
        .select('-_id')
        .lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async updateEmotionAsset(
    assetId: string,
    updateData: Partial<AvatarEmotionAsset>,
  ): Promise<AvatarEmotionAsset | null> {
    try {
      const updated = await AvatarEmotionAssetModel.findOneAndUpdate(
        { id: assetId },
        { ...updateData, updatedAt: new Date() },
        { new: true },
      )
        .select('-_id')
        .lean();
      return updated;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }
  async deleteEmotionAsset(assetId: string): Promise<boolean> {
    try {
      const result = await AvatarEmotionAssetModel.deleteOne({ id: assetId });
      return result.deletedCount > 0;
    } catch (error) {
      console.log(error.message);
      return false;
    }
  }

  async checkEmotionAssetExists(
    avatarId: string,
    emotionId: string,
    type: string,
  ): Promise<boolean> {
    try {
      const asset = await AvatarEmotionAssetModel.findOne({
        avatarId,
        emotionId,
        type,
      });
      return !!asset;
    } catch (error) {
      console.log(error.message);
      return false;
    }
  }

  async countEmotionAssetsByAvatar(avatarId: string): Promise<number> {
    try {
      return await AvatarEmotionAssetModel.countDocuments({ avatarId });
    } catch (error) {
      console.log(error.message);
      return 0;
    }
  }
}
