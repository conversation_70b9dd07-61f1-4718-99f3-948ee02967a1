import { Module } from '@nestjs/common';
import { DietModule } from '../diet/diet.rest.module';
import { UserModule } from '../user/user.rest.module';
import { AdminAvatarController } from './admin/controllers/avatar.controller';
import { AvatarRepository } from './admin/repositories/avatar.repository';
import { AdminAvatarService } from './admin/services/avatar.service';
import { UserAvatarController } from './user/controllers/user-avatar.controller';
import { UserAvatarRepository } from './user/repositories/user-avatar.repository';
import { AvatarEmotionService } from './user/services/user-avatar-emotion.service';
import { UserAvatarHelperService } from './user/services/user-avatar.helper.service';
import { UserAvatarService } from './user/services/user-avatar.service';

@Module({
  imports: [UserModule, DietModule],
  controllers: [AdminAvatarController, UserAvatarController],
  providers: [
    AdminAvatarService,
    AvatarRepository,
    UserAvatarService,
    UserAvatarRepository,
    UserAvatarHelperService,
    AvatarEmotionService,
  ],
  exports: [
    AdminAvatarService,
    UserAvatarService,
    AvatarRepository,
    UserAvatarRepository,
    AvatarEmotionService,
  ],
})
export class AvatarModule {}
