export enum AvatarGender {
  MALE = 'male',
  FEMALE = 'female',
}
export enum AvatarStyle {
  ANIME = 'anime',
  AVATAR = 'avatar',
}

export enum AvatarType {
  '2D' = '2d',
  '3D' = '3d',
}

export enum BodyStatus {
  UNDERWEIGHT = 'underweight',
  NORMAL = 'normal',
  OVERWEIGHT = 'overweight',
  OBESE = 'obese',
}

export enum AccessoryType {
  HANDHELD = 'handheld',
  CLOTHING = 'clothing',
  HEADWEAR = 'headwear',
  FOOTWEAR = 'footwear',
}

export enum AvatarEmotion {
  NEUTRAL = 'neutral',
  HUNGRY = 'hungry',
  SATISFIED = 'satisfied',
  SLEEPY = 'sleepy',
  HAPPY = 'happy',
}

export interface MealConfig {
  type: string;
  hungerDelayHours: number;
  nextMeal?: string;
}

export const MEAL_CONFIGS: MealConfig[] = [
  { type: 'breakfast', hungerDelayHours: 4, nextMeal: 'lunch' },
  { type: 'lunch', hungerDelayHours: 3, nextMeal: 'snacks' },
  { type: 'snacks', hungerDelayHours: 3, nextMeal: 'dinner' },
  { type: 'dinner', hungerDelayHours: 0 }, // No hunger after dinner
];

export const TIME_EMOTION_RULES = {
  NIGHT_START: 23, // 11 PM
  NIGHT_END: 6, // 6 AM
  MORNING_START: 6,
  MORNING_END: 12,
  AFTERNOON_START: 12,
  AFTERNOON_END: 18,
  EVENING_START: 18,
  EVENING_END: 22,
};
