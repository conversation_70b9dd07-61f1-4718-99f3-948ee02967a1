import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { AccessoryType } from '../const/enum';
import { Accessory } from '../entities/accessory';

const AccessorySchema = new Schema<Accessory>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    name: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: AccessoryType,
      required: true,
    },
    pointsRequired: {
      type: Number,
      required: true,
    },
    compatibleAvatars: [
      {
        type: String,
      },
    ],
    assetPath2D: {
      type: String,
    },
    assetPath3D: {
      type: String,
    },
    position: {
      '2d': String,
      '3d': String,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

export const AccessoryModel = model<Accessory>('accessory', AccessorySchema);
