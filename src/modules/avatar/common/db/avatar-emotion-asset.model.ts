import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { AvatarEmotion, AvatarType, BodyStatus } from '../const/enum';
import { AvatarEmotionAsset } from '../entities/avatar-emotion-asset';

const AvatarEmotionAssetSchema = new Schema<AvatarEmotionAsset>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    avatarId: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: AvatarType,
      required: true,
    },
    bodyStatus: {
      type: String,
      enum: BodyStatus,
      required: true,
    },
    emotion: {
      type: String,
      enum: AvatarEmotion,
      required: true,
    },
    assetPath: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

AvatarEmotionAssetSchema.index(
  { avatarId: 1, type: 1, bodyStatus: 1, emotion: 1 },
  { unique: true },
);

export const AvatarEmotionAssetModel = model<AvatarEmotionAsset>(
  'avatar-emotion-asset',
  AvatarEmotionAssetSchema,
);
