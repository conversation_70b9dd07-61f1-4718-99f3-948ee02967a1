import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { AvatarEmotion } from '../const/enum';
import { AvatarEmotionSupport } from '../entities/avatar-emotion';

const AvatarEmotionSchema = new Schema<AvatarEmotionSupport>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    avatarId: {
      type: String,
      required: true,
    },
    emotion: {
      type: String,
      enum: AvatarEmotion,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

AvatarEmotionSchema.index({ avatarId: 1, emotion: 1 }, { unique: true });

export const AvatarEmotionModel = model<AvatarEmotionSupport>(
  'avatar-emotion',
  AvatarEmotionSchema,
);
