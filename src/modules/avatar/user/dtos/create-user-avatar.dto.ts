import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsObject, IsString } from 'class-validator';
import {
  AvatarEmotion,
  AvatarStyle,
  AvatarType,
  BodyStatus,
} from '../../common/const/enum';

export class SelectAvatarDto {
  @ApiProperty()
  @IsString()
  avatarId: string;
}

export class RedeemAccessoryDto {
  @ApiProperty()
  @IsString()
  accessoryId: string;
}

export class SelectAvatarResponseDto {
  @Expose()
  @ApiProperty({ description: 'Unique identifier for the avatar state' })
  @IsString()
  id: string;

  @Expose()
  @ApiProperty({ description: 'User ID who selected the avatar' })
  @IsString()
  userId: string;

  @Expose()
  @ApiProperty({ description: 'Selected avatar ID' })
  @IsString()
  avatarId: string;

  @Expose()
  @ApiProperty({ description: 'Avatar style', enum: AvatarStyle })
  @IsEnum(AvatarStyle)
  avatarStyle: AvatarStyle;

  @Expose()
  @ApiProperty({ description: 'Avatar type', enum: AvatarType })
  @IsEnum(AvatarType)
  avatarType: AvatarType;

  @Expose()
  @ApiProperty({ description: 'Current avatar emotion', enum: AvatarEmotion })
  @IsEnum(AvatarEmotion)
  emotion: AvatarEmotion;

  @Expose()
  @ApiProperty({
    description: 'Body status based on user health data',
    enum: BodyStatus,
  })
  @IsEnum(BodyStatus)
  bodyStatus: BodyStatus;

  @Expose()
  @ApiProperty({ description: 'Base body asset URL for the selected avatar' })
  @IsString()
  url: string;
}

export class SelectAvatarSuccessResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Avatar selection result with user avatar state and asset URL',
  })
  @IsObject()
  @IsNotEmpty()
  data: SelectAvatarResponseDto;
}
