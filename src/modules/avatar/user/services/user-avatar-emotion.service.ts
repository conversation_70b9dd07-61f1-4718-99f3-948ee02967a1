import { Injectable } from '@nestjs/common';
import { DietService } from 'src/modules/diet/services';
import {
  AvatarEmotion,
  MEAL_CONFIGS,
  TIME_EMOTION_RULES,
} from '../../common/const/enum';
import { UserAvatarRepository } from '../repositories/user-avatar.repository';

export interface EmotionResult {
  emotion: AvatarEmotion;
  assetPath: string;
  reason: string;
}

@Injectable()
export class AvatarEmotionService {
  constructor(
    private readonly dietService: DietService,
    private readonly userAvatarRepo: UserAvatarRepository,
  ) {}

  async calculateDietBasedEmotion(userId: string): Promise<EmotionResult> {
    const avatarState = await this.userAvatarRepo.findUserAvatarState(userId);
    // console.log('Avatar state:', avatarState);

    if (!avatarState) {
      throw new Error('User has no avatar selected');
    }

    const now = new Date();
    const currentHour = now.getHours();
    console.log('Current hour:', currentHour);

    // Night time (10 PM - 6 AM) = Sleepy
    if (this.isNightTime(currentHour)) {
      console.log('Night time detected');
      return this.createEmotionResult(
        AvatarEmotion.SLEEPY,
        'Night time - sleepy',
        avatarState.avatarId,
        avatarState.avatarType,
        avatarState.bodyStatus,
      );
    }

    try {
      // Get today's diet history using the existing diet service
      const dietHistoryResponse =
        await this.dietService.getDietHistoryListByUserId(userId, {
          date: new Date(),
        });

      const dietHistory = dietHistoryResponse.data;

      if (!dietHistory) {
        console.log('No diet history found');
        return this.getDefaultEmotionByTime(currentHour, avatarState);
      }

      const allConsumedFoods = [
        ...(dietHistory.breakfast || []),
        ...(dietHistory.lunch || []),
        ...(dietHistory.dinner || []),
        ...(dietHistory.snacks || []),
      ];

      console.log('All consumed foods:', allConsumedFoods);

      if (!allConsumedFoods.length) {
        console.log('No consumed foods found');
        return this.getDefaultEmotionByTime(currentHour, avatarState);
      }

      // Check if recently ate (within 1 hour) - Move this BEFORE hunger check
      const hasRecent = this.hasRecentMeal(allConsumedFoods, now);
      console.log('Has recent meal:', hasRecent);
      if (hasRecent) {
        console.log('Recently ate - satisfied');
        return this.createEmotionResult(
          AvatarEmotion.SATISFIED,
          'Recently ate - satisfied',
          avatarState.avatarId,
          avatarState.avatarType,
          avatarState.bodyStatus,
        );
      }

      // Check for hunger based on meal timing
      const hungerEmotion = await this.checkMealBasedHunger(
        allConsumedFoods,
        now,
        avatarState,
      );
      console.log('hungerEmotion result:', hungerEmotion);
      if (hungerEmotion) {
        return hungerEmotion;
      }

      // Default emotion based on time
      console.log('Falling back to default emotion by time');
      return this.getDefaultEmotionByTime(currentHour, avatarState);
    } catch (error) {
      console.log('Error fetching diet history for emotion:', error);
      return this.getDefaultEmotionByTime(currentHour, avatarState);
    }
  }

  private async checkMealBasedHunger(
    consumedFoods: any[],
    currentTime: Date,
    avatarState: any,
  ): Promise<EmotionResult | null> {
    // Get logged meal types for today
    const mealTypes = new Set(consumedFoods.map((food) => food.mealType));

    console.log('Meal types:', mealTypes);

    for (const config of MEAL_CONFIGS) {
      // If current meal is logged but next meal is not
      if (
        mealTypes.has(config.type) &&
        config.nextMeal &&
        !mealTypes.has(config.nextMeal)
      ) {
        // NEW: Check if user has eaten a later meal (meal skipping scenario)
        const hasLaterMeal = this.hasEatenLaterMeal(config.nextMeal, mealTypes);
        console.log('Has later meal:', hasLaterMeal);
        if (hasLaterMeal) {
          continue; // Skip hunger check if user has eaten a later meal
        }
        // Find the latest time this meal was logged
        const mealTime = this.getLatestMealTime(consumedFoods, config.type);
        console.log('Latest meal time for', config.type, ':', mealTime);

        if (mealTime) {
          // Calculate hours since meal
          const hoursSinceMeal =
            (currentTime.getTime() - mealTime.getTime()) / (1000 * 60 * 60);

          // If enough time has passed, show hungry
          if (hoursSinceMeal >= config.hungerDelayHours) {
            return await this.createEmotionResult(
              AvatarEmotion.HUNGRY,
              `Hungry for ${config.nextMeal} - ${config.hungerDelayHours}h after ${config.type}`,
              avatarState.avatarId,
              avatarState.avatarType,
              avatarState.bodyStatus,
            );
          }
        }
      }
    }

    return null;
  }

  private getLatestMealTime(
    consumedFoods: any[],
    mealType: string,
  ): Date | null {
    const mealFoods = consumedFoods.filter(
      (food) => food.mealType === mealType,
    );

    if (!mealFoods.length) return null;

    // Sort by creation time and get the latest
    const latestMeal = mealFoods.sort(
      (a, b) =>
        new Date(b.createdAt || b.timestamp).getTime() -
        new Date(a.createdAt || a.timestamp).getTime(),
    )[0];

    return new Date(latestMeal.createdAt || latestMeal.timestamp);
  }

  private hasRecentMeal(consumedFoods: any[], currentTime: Date): boolean {
    if (!consumedFoods.length) return false;

    const latestMeal = consumedFoods.sort(
      (a, b) =>
        new Date(b.createdAt || b.timestamp).getTime() -
        new Date(a.createdAt || a.timestamp).getTime(),
    )[0];

    const latestMealTime = new Date(
      latestMeal.createdAt || latestMeal.timestamp,
    );
    const hoursSinceLastMeal =
      (currentTime.getTime() - latestMealTime.getTime()) / (1000 * 60 * 60);

    return hoursSinceLastMeal <= 1; // Within 1 hour
  }

  private isNightTime(hour: number): boolean {
    return (
      hour >= TIME_EMOTION_RULES.NIGHT_START ||
      hour < TIME_EMOTION_RULES.NIGHT_END
    );
  }

  private async getDefaultEmotionByTime(
    hour: number,
    avatarState: any,
  ): Promise<EmotionResult> {
    if (
      hour >= TIME_EMOTION_RULES.MORNING_START &&
      hour < TIME_EMOTION_RULES.MORNING_END
    ) {
      console.log('Morning time - neutral');
      return this.createEmotionResult(
        AvatarEmotion.NEUTRAL,
        'Morning time - neutral',
        avatarState.avatarId,
        avatarState.avatarType,
        avatarState.bodyStatus,
      );
    } else if (
      hour >= TIME_EMOTION_RULES.AFTERNOON_START &&
      hour < TIME_EMOTION_RULES.EVENING_START
    ) {
      console.log('Afternoon time - neutral');
      return this.createEmotionResult(
        AvatarEmotion.NEUTRAL,
        'Afternoon time - neutral',
        avatarState.avatarId,
        avatarState.avatarType,
        avatarState.bodyStatus,
      );
    } else {
      console.log('Evening time - neutral');
      return this.createEmotionResult(
        AvatarEmotion.NEUTRAL,
        'Evening time - neutral',
        avatarState.avatarId,
        avatarState.avatarType,
        avatarState.bodyStatus,
      );
    }
  }

  private async createEmotionResult(
    emotion: AvatarEmotion,
    reason: string,
    avatarId: string,
    avatarType: any,
    bodyStatus: any,
  ): Promise<EmotionResult> {
    // Try to get the actual asset from database
    const emotionAsset = await this.userAvatarRepo.findEmotionAssetByConditions(
      avatarId,
      avatarType,
      bodyStatus,
      emotion,
    );

    console.log('Emotion asset:', emotionAsset);

    return {
      emotion,
      assetPath: emotionAsset?.assetPath || '', // Return actual assetPath or empty string
      reason,
    };
  }

  private hasEatenLaterMeal(
    missedMeal: string,
    mealTypes: Set<string>,
  ): boolean {
    const mealOrder = ['breakfast', 'lunch', 'snacks', 'dinner'];
    const missedMealIndex = mealOrder.indexOf(missedMeal);

    if (missedMealIndex === -1) return false;

    // Check if any meal after the missed meal has been eaten
    for (let i = missedMealIndex + 1; i < mealOrder.length; i++) {
      if (mealTypes.has(mealOrder[i])) {
        return true; // User has eaten a later meal, so they skipped the missed meal
      }
    }

    return false;
  }
}
