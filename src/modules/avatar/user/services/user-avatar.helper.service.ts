import { Injectable } from '@nestjs/common';
import { AvatarType, BodyStatus } from '../../common/const/enum';
import { UserHealthData } from '../../common/const/interface';
import { UserAvatarRepository } from '../repositories/user-avatar.repository';

@Injectable()
export class UserAvatarHelperService {
  constructor(private readonly userAvatarRepo: UserAvatarRepository) {}

  private convertWeightToKg(weight: number, weightType: 'kg' | 'lb'): number {
    return weightType === 'kg' ? weight : weight * 0.453592;
  }

  private convertHeightToCm(height: number, heightType: string): number {
    switch (heightType) {
      case 'ft':
        return height * 30.48; // feet to cm
      case 'in':
        return height * 2.54; // inches to cm
      case 'cm':
        return height;
      default:
        return height; // assume cm
    }
  }

  async calculateBodyStatusFromUserData(userData: UserHealthData) {
    let weightInkg;
    let heightInCm;
    if (userData.weight) {
      const weightType = userData.weightType || 'kg';
      weightInkg = this.convertWeightToKg(userData.weight, weightType);
      // console.log('Converted weight to kg:', weightInkg);
    }
    if (userData.height) {
      const heightType = userData.heightType || 'cm';
      heightInCm = this.convertHeightToCm(userData.height, heightType);
      // console.log('Converted height to cm:', heightInCm);
    }

    const bmiResult = await this.calculateBMI(weightInkg, heightInCm);
    // console.log(
    //   `BMI Calculation: Weight: ${weightInkg}kg, Height: ${heightInCm}cm, BMI: ${bmiResult.bmi}, Category: ${bmiResult.category}, Body Status: ${bmiResult.bodyStatus}`,
    // );

    return bmiResult.bodyStatus;
  }

  private async calculateBMI(weightInkg: number, heightInCm) {
    const heightM = heightInCm / 100; // convert cm to meters
    const bmi = weightInkg / (heightM * heightM);
    let category: 'underweight' | 'normal' | 'overweight' | 'obese';
    let bodyStatus: BodyStatus;
    if (bmi < 18.5) {
      category = 'underweight';
      bodyStatus = BodyStatus.UNDERWEIGHT;
    } else if (bmi < 24.9) {
      category = 'normal';
      bodyStatus = BodyStatus.NORMAL;
    } else if (bmi < 29.9) {
      category = 'overweight';
      bodyStatus = BodyStatus.OVERWEIGHT;
    } else {
      category = 'obese';
      bodyStatus = BodyStatus.OBESE;
    }

    // console.log('Rounded BMI:', Math.round(bmi * 10) / 10);

    return {
      bmi: Math.round(bmi * 10) / 10, // Round to 1 decimal place
      category,
      bodyStatus,
      weightInkg,
      heightInCm,
    };
  }

  async getAvatarBaseBody(
    avatarId: string,
    bodyStatus: BodyStatus,
    avatarType: AvatarType,
  ): Promise<string> {
    try {
      const baseBody =
        await this.userAvatarRepo.findBaseBodyByAvatarIdAndBodyStatus(
          avatarId,
          bodyStatus,
          avatarType,
        );

      if (baseBody) {
        return baseBody.assetPath;
      }
      return '';
    } catch (error) {
      console.error('Error fetching avatar base body:', error);
    }
  }
}
