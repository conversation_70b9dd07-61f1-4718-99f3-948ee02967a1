import { HttpStatus, Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { deepCasting } from 'src/internal/casting/object.casting';
import { APIException } from 'src/internal/exception/api.exception';
import { DietUserInfoProvider } from 'src/modules/diet/providers';
import { UserInfoProvider } from 'src/modules/user/providers/user.provider'; // Use this instead
import { AvatarEmotion, BodyStatus } from '../../common/const/enum';
import { UserAvatarState } from '../../common/entities/user-avatar';
import {
  SelectAvatarResponseDto,
  SelectAvatarSuccessResponseDto,
} from '../dtos/create-user-avatar.dto';
import {
  GetAvailableAvatarsResponseDto,
  GetAvailableAvatarsSuccessResponseDto,
} from '../dtos/get-user-avatars.dto';
import { UserAvatarRepository } from '../repositories/user-avatar.repository';
import { AvatarEmotionService } from './user-avatar-emotion.service';
import { UserAvatarHelperService } from './user-avatar.helper.service';

@Injectable()
export class UserAvatarService {
  constructor(
    private readonly userAvatarRepo: UserAvatarRepository,
    private readonly userInfoProvider: UserInfoProvider,
    private readonly dietUserInfoProvider: DietUserInfoProvider,
    private readonly userAvatarHelper: UserAvatarHelperService,
    private readonly helper: Helper,
    private readonly avatarEmotionService: AvatarEmotionService,
  ) {}

  async getAvailableAvatars(
    userId: string,
  ): Promise<GetAvailableAvatarsSuccessResponseDto> {
    const avatars = await this.userAvatarRepo.findAllActiveAvatars();
    let responseDtos;

    const dietUserInfo =
      await this.dietUserInfoProvider.getUserInfoFromDietPlan(userId);
    if (dietUserInfo?.data) {
      const filterAvatar = avatars.filter(
        (avatar) => avatar.gender === dietUserInfo.data.gender,
      );
      responseDtos = filterAvatar.map((avatar) =>
        deepCasting(GetAvailableAvatarsResponseDto, avatar),
      );

      return this.helper.serviceResponse.successResponse(responseDtos);
    }

    // If no diet user info is found, return all avatars
    responseDtos = avatars.map((avatar) =>
      deepCasting(GetAvailableAvatarsResponseDto, avatar),
    );

    return this.helper.serviceResponse.successResponse(responseDtos);
  }

  async selectAvatar(
    userId: string,
    avatarId: string,
  ): Promise<SelectAvatarSuccessResponseDto> {
    // Verify avatar exists
    const selectedAvatar = await this.userAvatarRepo.findAvatarById(avatarId);
    if (!selectedAvatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    // Check if user already has an avatar
    const existingAvatarState = await this.userAvatarRepo.findUserAvatarState(
      userId,
    );
    if (existingAvatarState) {
      throw new APIException(
        'User already has an avatar selected',
        'AVATAR_ALREADY_SELECTED',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Get user health data from diet plan - REQUIRED
    let userHealthData;

    try {
      const dietUserInfo =
        await this.dietUserInfoProvider.getUserInfoFromDietPlan(userId);
      // console.log('Diet user info:', dietUserInfo);

      if (!dietUserInfo?.data) {
        throw new APIException(
          'You must create a diet plan first before selecting an avatar',
          'DIET_PLAN_REQUIRED',
          HttpStatus.BAD_REQUEST,
        );
      }

      // Pass raw data to helper service - it will handle all conversions
      userHealthData = {
        weight: dietUserInfo.data.weight,
        weightType: dietUserInfo.data.weightType,
        height: dietUserInfo.data.height,
        heightType: dietUserInfo.data.heightType,
      };
    } catch (error) {
      // If it's any other error (like network error), throw diet plan required error
      throw new APIException(
        'You must create a diet plan first before selecting an avatar',
        'DIET_PLAN_REQUIRED',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Helper service handles ALL conversions and calculations
    const bodyStatus =
      await this.userAvatarHelper.calculateBodyStatusFromUserData(
        userHealthData,
      );

    const baseBodyAssetPath = await this.userAvatarHelper.getAvatarBaseBody(
      selectedAvatar.id,
      bodyStatus as BodyStatus,
      selectedAvatar.type,
    );

    const avatarState: UserAvatarState = {
      userId,
      avatarId: selectedAvatar.id,
      avatarStyle: selectedAvatar.style,
      avatarType: selectedAvatar.type,
      emotion: AvatarEmotion.NEUTRAL,
      bodyStatus: bodyStatus as BodyStatus,
      accessories: [],
      lastUpdated: new Date(),
    };

    const result = await this.userAvatarRepo.createUserAvatarState(avatarState);

    if (!result) {
      throw new APIException(
        'Failed to create avatar state',
        'AVATAR_STATE_CREATION_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const responseDto = deepCasting(SelectAvatarResponseDto, {
      ...result,
      url: baseBodyAssetPath || [],
    });

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getUserAvatarState(userId: string): Promise<any> {
    try {
      // Get existing avatar state
      const avatarState = await this.userAvatarRepo.findUserAvatarState(userId);

      if (!avatarState) {
        throw new APIException(
          'User avatar not found. Please select an avatar first.',
          'AVATAR_NOT_SELECTED',
          HttpStatus.NOT_FOUND,
        );
      }

      // Get avatar details
      const avatar = await this.userAvatarRepo.findAvatarById(
        avatarState.avatarId,
      );

      if (!avatar) {
        throw new APIException(
          'Avatar details not found',
          'AVATAR_DETAILS_NOT_FOUND',
          HttpStatus.NOT_FOUND,
        );
      }
      const emotionData =
        await this.avatarEmotionService.calculateDietBasedEmotion(userId);

      // Prepare response data
      const responseData = {
        userId: avatarState.userId,
        currentAvatarId: avatarState.avatarId,
        avatarName: avatar.name,

        bodyStatus: avatarState.bodyStatus,

        emotion: emotionData.emotion,
        emotionAssetPath: emotionData.assetPath,
        emotionReason: emotionData.reason,
        createdAt: avatarState.createdAt,
        updatedAt: avatarState.updatedAt,
      };

      return this.helper.serviceResponse.successResponse(responseData);
    } catch (error) {
      if (error instanceof APIException) {
        throw error;
      }
      console.error('Error getting avatar state:', error);
      throw new APIException(
        'Failed to get avatar state',
        'AVATAR_STATE_ERROR',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateEmotionBasedOnDiet(userId: string, dietMet: boolean) {
    const avatarState = await this.userAvatarRepo.findUserAvatarState(userId);

    if (!avatarState) {
      return; // User hasn't selected avatar yet
    }

    const emotion = dietMet ? AvatarEmotion.HAPPY : AvatarEmotion.NEUTRAL;

    await this.userAvatarRepo.updateUserAvatarState(userId, { emotion });
  }

  async getAvailableAccessories(userId: string) {
    const avatarState = await this.userAvatarRepo.findUserAvatarState(userId);

    if (!avatarState) {
      throw new APIException(
        'Please select an avatar first',
        'NO_AVATAR_SELECTED',
        HttpStatus.BAD_REQUEST,
      );
    }

    const allAccessories = await this.userAvatarRepo.findAllActiveAccessories();

    // Filter accessories compatible with user's avatar
    const compatibleAccessories = allAccessories.filter((accessory) =>
      accessory.compatibleAvatars.includes(avatarState.avatarId),
    );

    return this.helper.serviceResponse.successResponse(compatibleAccessories);
  }

  async redeemAccessory(userId: string, accessoryId: string) {
    const accessory = await this.userAvatarRepo.findAccessoryById(accessoryId);

    if (!accessory) {
      throw new APIException(
        'Accessory not found',
        'ACCESSORY_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const avatarState = await this.userAvatarRepo.findUserAvatarState(userId);

    if (!avatarState) {
      throw new APIException(
        'Please select an avatar first',
        'NO_AVATAR_SELECTED',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if accessory is compatible with user's avatar
    if (!accessory.compatibleAvatars.includes(avatarState.avatarId)) {
      throw new APIException(
        'Accessory not compatible with selected avatar',
        'INCOMPATIBLE_ACCESSORY',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if user already owns this accessory
    if (avatarState.accessories.includes(accessoryId)) {
      throw new APIException(
        'Accessory already owned',
        'ACCESSORY_ALREADY_OWNED',
        HttpStatus.BAD_REQUEST,
      );
    }

    // TODO: Check if user has enough points (integrate with user points service)
    // const userPoints = await this.userPointsService.myPoints(userId);
    // if (userPoints.data.points < accessory.pointsRequired) {
    //   throw new APIException(
    //     'Insufficient points',
    //     'INSUFFICIENT_POINTS',
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    // TODO: Deduct points
    // await this.userPointsService.updatePoints(userId, {
    //   type: 'spent',
    //   points: accessory.pointsRequired,
    // });

    // Add accessory to user's collection
    const updatedAccessories = [...avatarState.accessories, accessoryId];
    const updatedState = await this.userAvatarRepo.updateUserAvatarState(
      userId,
      {
        accessories: updatedAccessories,
      },
    );

    return this.helper.serviceResponse.successResponse(updatedState);
  }
}
