import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import {
  IChallengeDurationEnum,
  IUserChallengeStatusEnum,
  IUserPointsSources,
  IUserPointsTransTypeEnum,
  NotificationCategory,
  NotificationModule,
  NotificationType,
} from 'models';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { UserPointsService } from 'src/modules/user-points/services';
import { UserRepository } from 'src/modules/user/repositories';
import { BaseChallengeRepository } from '../repositories';
import { UserChallengeRepository } from '../repositories/user.challenge.repository';

@Injectable()
export class ChallengeApprovalService {
  constructor(
    private readonly baseChallengeRepository: BaseChallengeRepository,
    private readonly userChallengeRepository: UserChallengeRepository,
    private readonly userPointsService: UserPointsService,
    private readonly userRepository: UserRepository,
    private readonly notificationHelperService: NotificationHelperService,
  ) {}

  /**
   * Cron job for daily challenges - runs at 6 AM every day
   * Auto-approves pending daily challenges
   */
  @Cron('0 6 * * *') // 6 AM daily
  async autoApproveDailyChallenges() {
    console.log('Running cron job for auto-approving daily challenges');
    await this.autoApprovePendingChallengesByDuration(
      IChallengeDurationEnum.DAILY,
    );
  }

  /**
   * Cron job for weekly challenges - runs at 6 AM every Monday
   * Auto-approves pending weekly challenges
   */
  @Cron('0 6 * * 1') // 6 AM every Monday
  async autoApproveWeeklyChallenges() {
    console.log('Running cron job for auto-approving weekly challenges');
    await this.autoApprovePendingChallengesByDuration(
      IChallengeDurationEnum.WEEKLY,
    );
  }

  /**
   * Cron job for monthly challenges - runs at 6 AM on the 1st of every month
   * Auto-approves pending monthly challenges
   */
  @Cron('0 6 1 * *') // 6 AM on 1st day of every month
  async autoApproveMonthilyChallenges() {
    console.log('Running cron job for auto-approving monthly challenges');
    await this.autoApprovePendingChallengesByDuration(
      IChallengeDurationEnum.MONTHLY,
    );
  }

  /**
   * Fallback cron job - runs at midnight daily for any remaining pending challenges
   * regardless of duration type
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async autoApprovePendingChallenges() {
    console.log(
      'Running fallback cron job for auto-approving pending challenges',
    );
    await this.autoApprovePendingChallengesByDuration();
  }

  /**
   * Helper method to approve challenges by duration type
   */
  private async autoApprovePendingChallengesByDuration(
    durationType?: IChallengeDurationEnum,
  ) {
    try {
      const pendingChallenges =
        await this.userChallengeRepository.findPendingChallenges();

      // Filter by duration type if specified
      let filteredChallenges = pendingChallenges;
      if (durationType) {
        // You'll need to join with base challenges to get duration type
        filteredChallenges = await this.filterChallengesByDuration(
          pendingChallenges,
          durationType,
        );
      }

      console.log(
        `Found ${filteredChallenges.length} pending ${
          durationType || 'all'
        } challenges to approve`,
      );

      // Process each pending challenge
      for (const challenge of filteredChallenges) {
        // Update challenge status to COMPLETED
        const setData = {
          lastStatus: IUserChallengeStatusEnum.COMPLETED,
        };

        const query = {
          id: challenge.id,
          lastStatus: IUserChallengeStatusEnum.PENDING,
        };

        const updated =
          await this.baseChallengeRepository.reviewAndChangeStatus(
            query,
            setData,
          );

        if (updated && updated.points > 0) {
          // Award points to user
          const updatePoints = await this.userPointsService.updatePoints(
            updated.userId,
            {
              type: IUserPointsTransTypeEnum.EARNED,
              points: updated.points,
              pointSourceType: IUserPointsSources.Challenges,
              pointSourceId: challenge.challengeId,
              pointSourceName: challenge.challengeName || '',
            },
          );

          if (updatePoints) {
            // Get user details for notification
            const user = await this.userRepository.findUser({
              id: updated.userId,
            });

            // Send notification to user
            const payload = {
              recipient: user,
              createdBy: {
                name: 'system',
              },
              title: 'Your challenge has been approved!',
              content: `Your ${
                durationType || ''
              } challenge has been automatically approved and you've earned ${
                updated.points
              } points!`,
              module: NotificationModule.CHALLENGE,
              type: NotificationType.CHALLENGE_STATUS,
              category: NotificationCategory.CHALLENGE_STATUS,
              documentId: updated.id,
            };

            await this.notificationHelperService.sendNotifications(payload);
            console.log(
              `Successfully approved ${durationType || ''} challenge ${
                updated.id
              } for user ${updated.userId}`,
            );
          }
        }
      }

      console.log(
        `Finished auto-approving pending ${durationType || 'all'} challenges`,
      );
    } catch (error) {
      console.error(
        `Error in auto-approving pending ${durationType || 'all'} challenges:`,
        error.message,
      );
    }
  }

  /**
   * Filter challenges by duration type - joins with BaseChallengeModel to get durationType
   */
  private async filterChallengesByDuration(
    challenges: any[],
    durationType: IChallengeDurationEnum,
  ) {
    try {
      // Extract challenge IDs from user challenges
      const challengeIds = challenges.map((challenge) => challenge.challengeId);

      // Find base challenges with matching IDs and duration type using repository
      const baseChallenges =
        await this.baseChallengeRepository.findByIdsAndDurationType(
          challengeIds,
          durationType,
        );

      if (!baseChallenges) {
        console.error('Error fetching base challenges');
        return [];
      }

      // Create a Set of base challenge IDs that match the duration type
      const matchingChallengeIds = new Set(baseChallenges.map((bc) => bc.id));

      // Filter user challenges to only include those with matching duration type
      const filteredChallenges = challenges.filter((challenge) =>
        matchingChallengeIds.has(challenge.challengeId),
      );

      return filteredChallenges;
    } catch (error) {
      console.error('Error filtering challenges by duration:', error.message);
      return [];
    }
  }
}
