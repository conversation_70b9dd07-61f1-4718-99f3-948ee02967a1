import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export enum MeetingScope {
  VOIP = 'voip',
}

export class CreateAccessTokenRequestDto {
  @ApiProperty()
  @IsString()
  communicationUserId: string;
}

export class CreateAccessTokenResponseDto {
  @ApiProperty({
    description: 'Access token for Azure Communication Services',
  })
  token: string;

  @ApiProperty({
    description: 'Token expiration date',
  })
  expiresOn: Date;

  @ApiProperty({
    description: 'Communication user ID',
  })
  communicationUserId: string;
}

export class CreateIdentityRequestDto {
  @ApiProperty({
    description: 'Custom ID to associate with the identity (optional)',
    example: '<EMAIL>',
    required: false,
  })
  @IsOptional()
  @IsString()
  customId?: string;
}

export class CreateIdentityResponseDto {
  @ApiProperty({
    description: 'Communication user ID',
  })
  communicationUserId: string;

  @ApiProperty({
    description: 'Custom ID associated with the identity',
    required: false,
  })
  customId?: string;
}


export class CreateIdentityAndTokenResponseDto {
  @ApiProperty({
    description: 'Access token for Azure Communication Services',
    example: '****************************************************...',
  })
  token: string;

  @ApiProperty({
    description: 'Token expiration date',
    example: '2025-07-30T12:00:00.000Z',
  })
  expiresOn: Date;

  @ApiProperty({
    description: 'Communication user ID',
  })
  communicationUserId: string;

  @ApiProperty({
    description: 'Custom ID associated with the identity',
    required: false,
  })
  customId?: string;
}