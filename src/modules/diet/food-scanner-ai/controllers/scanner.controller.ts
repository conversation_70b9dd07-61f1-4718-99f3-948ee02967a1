import {
  Controller,
  HttpStatus,
  Post,
  UploadedFile,
  UseGuards
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { OnlyAuthGuard, OnlyRoleGuard, Role, Roles } from 'src/authentication/guards/auth-role.guard';
import { User as UserInfo } from 'src/decorators/auth.decorator';
import { ApiFile } from 'src/decorators/file.decorator';
import { User } from 'src/entity/user';
import { FoodScannerResponseDto } from '../dtos/scanner.dto';
import { FoodScannerService } from '../services/scanner.service';


@Controller('food-scanner')
@ApiTags('Food Scanner API')
export class FoodScannerController {
  constructor(private readonly foodScannerService: FoodScannerService) {}
  
  @Post('analyze')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @ApiBearerAuth()
  @Roles(Role.User)
  @ApiOperation({ summary: 'Analyze food image' })
  @ApiFile('file', true, {})
  @ApiResponse({
    description: 'Returns the food analysis result',
    type: FoodScannerResponseDto,
    isArray: true,
    status: HttpStatus.OK,
  })
  async analyzeFoodImage(
    @UploadedFile('file') file: Express.Multer.File,
    @UserInfo() user: User
  ) {
    return await this.foodScannerService.analyzeFoodImage({
      file,
      userId: user.id 
    });
  }
}
