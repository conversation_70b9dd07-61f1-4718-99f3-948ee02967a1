import { ApiProperty } from '@nestjs/swagger';
import { Expose } from "class-transformer";
import { IsArray, IsNotEmpty, IsNumber, IsString } from "class-validator";

export class FoodScannerDto {
  @Expose()
  @Expose()
  @ApiProperty({ description: 'English food/dish name' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ description: 'বাংলা নাম (Bangla script)', required: false })
  @IsString()
  knownAs?: string;

  @Expose()
  @ApiProperty({ description: 'Number of servings (use 1 if unsure)', example: 1 })
  @IsNumber()
  @IsNotEmpty()
  servingSize: number;

  @Expose()
  @ApiProperty({ description: 'Calories (kcal) for the provided serving size', example: 250 })
  @IsNumber()
  @IsNotEmpty()
  calories: number;

  @Expose()
  @Expose()
  @ApiProperty({ description: 'Carbohydrates in grams', example: 30 })
  @IsNumber()
  @IsNotEmpty()
  carb: number;

  @Expose()
  @ApiProperty({ description: 'Fat in grams', example: 10 })
  @IsNumber()
  @IsNotEmpty()
  fat: number;

  @Expose()
  @ApiProperty({ description: 'Protein in grams', example: 12 })
  @IsNumber()
  @IsNotEmpty()
  protein: number;

  @Expose()
  @ApiProperty({ description: 'Confidence score between 0 and 1', example: 0.85 })
  @IsNumber()
  @IsNotEmpty()
  confidence: number;

  @Expose()
  @ApiProperty({ description: 'Visible ingredients', isArray: true, type: String, required: false })
  @IsArray()
  @IsNotEmpty()
  foodItems?: string[];
}


export class FoodScannerResponseDto {
  @Expose()
  @ApiProperty({ required: true, type: FoodScannerDto })
  @IsArray()
  @IsNotEmpty()
  data: FoodScannerDto[];

  @Expose()
  @ApiProperty({ description: 'Indicates whether food was detected in the image', example: true })
  @IsNotEmpty()
  isFoodDetected: boolean;
}