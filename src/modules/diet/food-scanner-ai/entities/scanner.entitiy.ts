export interface FoodRecognitionResult {
  name: string; // canonical name used for DB storage (prefer English)
  knownAs?: string; // বাংলা নাম (Bangla script)
  servingSize: number;
  calories: number;
  carb: number;
  fat: number;
  protein: number;
  confidence: number;
  foodItems?: string[];
}

export interface ScannerRequest {
  file: Express.Multer.File;
  userId?: string;
}

export interface FoodScannerEntity {
  id?: string;
  userId?: string;
  imageUrl: string;
  foodInfo: FoodRecognitionResult[];
  isFoodDetected: boolean;
}
