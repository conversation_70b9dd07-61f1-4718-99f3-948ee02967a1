import { Module } from '@nestjs/common';
import { AIModule } from 'src/modules/ai/ai.module';
import { FoodScannerController } from './controllers/scanner.controller';
import { ScannerRepository } from './repositories/scanner.repository';
import { FoodScannerService } from './services/scanner.service';

@Module({
  imports: [AIModule],
  controllers: [FoodScannerController],
  providers: [FoodScannerService, ScannerRepository],
  exports: [FoodScannerService]
})
export class FoodScannerModule {}
