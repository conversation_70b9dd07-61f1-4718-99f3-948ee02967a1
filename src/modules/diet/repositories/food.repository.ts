import { Injectable } from '@nestjs/common';
import { FoodModel } from 'src/database/food/food.model';
import { Food } from 'src/entity/food';
import { CreateFoodDto } from '../rest/dto/food/createFood.dto';
import { UpdateFoodDto } from '../rest/dto/food/updateFood.dto';

@Injectable()
export class FoodRepository {
  async createFood(createFoodDto: CreateFoodDto): Promise<Food> {
    const foodModel = new FoodModel(createFoodDto);
    const foodDocument = await foodModel.save();
    return foodDocument.toObject();
  }

  async updateFood(
    id: string,
    updateFoodDto: UpdateFoodDto,
  ): Promise<Food | null> {
    return await FoodModel.findOneAndUpdate(
      { id },
      { $set: updateFoodDto },
      {
        new: true,
        runValidators: true,
      },
    );
  }

  async getFoodById(id: string): Promise<Food | null> {
    const searchQuery = { id: id, isDeleted: false };
    const foodDocument = await FoodModel.findOne(searchQuery).exec();
    return foodDocument !== null ? foodDocument.toObject() : null;
  }

  async deleteFood(id: string): Promise<Food | null> {
    const newInfo = { isDeleted: true };
    const updatedEventDocument = await FoodModel.findOneAndUpdate(
      { id: id },
      { $set: newInfo },
      { new: true, runValidators: true },
    ).exec();
    return updatedEventDocument.toObject();
  }

  async getAllFoods(offset: number, limit: number): Promise<Food[]> {
    return await FoodModel.find()
      .sort({ createdAt: -1 })
      .skip(offset)
      .limit(limit)
      .lean();
  }

  async getAllFoodsBySearch(search: string): Promise<Food[]> {
    let query: any = { isDeleted: false };

    if (search) {
      query = {
        isDeleted: false,
        $or: [
          { foodNameEnglish: { $regex: search, $options: 'i' } },
          { foodNameBengali: { $regex: search, $options: 'i' } },
        ],
      };
    }

    return await FoodModel.find(query).sort({ createdAt: -1 });
  }
}
