// Add these imports at the top
import { Injectable } from '@nestjs/common';

import { WeightLogModel } from 'src/database/diet/weightLog.model';
import { WeightLog } from 'src/entity/globalFood';

@Injectable()
export class WeightLogRepository {
  async createWeightLog(
    weightLogData: Partial<WeightLog>,
  ): Promise<WeightLog | null> {
    try {
      const weightLog = await WeightLogModel.create({
        ...weightLogData,
      });

      const newWeightLog = weightLog.toObject();
      delete newWeightLog._id;
      return newWeightLog;
    } catch (error: any) {
      console.log('Error creating weight log:', error.message);
      return null;
    }
  }

  async getWeightLogsByUserId(
    userId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<WeightLog[] | null> {
    try {
      const query: any = { userId };

      if (startDate || endDate) {
        query.date = {};
        if (startDate) query.date.$gte = startDate;
        if (endDate) query.date.$lte = endDate;
      }

      const weightLogs = await WeightLogModel.find(query)
        .sort({ date: -1 })
        .select('-_id')
        .lean();

      return weightLogs;
    } catch (error: any) {
      console.log('Error getting weight logs:', error.message);
      return null;
    }
  }

  async getLatestWeightLog(userId: string): Promise<WeightLog | null> {
    try {
      const latestLog = await WeightLogModel.findOne({ userId })
        .sort({ date: -1 })
        .select('-_id')
        .lean();

      return latestLog;
    } catch (error: any) {
      console.log('Error getting latest weight log:', error.message);
      return null;
    }
  }

  async getFirstWeightLog(userId: string): Promise<WeightLog | null> {
    try {
      const firstLog = await WeightLogModel.findOne({ userId })
        .sort({ date: 1 })
        .select('-_id')
        .lean();

      return firstLog;
    } catch (error: any) {
      console.log('Error getting first weight log:', error.message);
      return null;
    }
  }
}
