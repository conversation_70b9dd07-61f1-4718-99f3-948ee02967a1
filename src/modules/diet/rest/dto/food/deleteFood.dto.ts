import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsObject, IsString } from 'class-validator';

export class DeleteFoodMessageDto {
  @Expose()
  @ApiProperty({ description: 'Success message' })
  @IsString()
  message: string;
}

export class DeleteFoodSuccessResponseDto {
  @Expose()
  @ApiProperty({ type: DeleteFoodMessageDto })
  @Type(() => DeleteFoodMessageDto)
  @IsObject()
  data: DeleteFoodMessageDto;
}
