import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsNotEmpty, IsObject } from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';

// Import or define the MineralsDto and VitaminsDto classes
class MineralsDto {
  @Expose()
  @ApiProperty()
  ca?: string;

  @Expose()
  @ApiProperty()
  fe?: string;

  @Expose()
  @ApiProperty()
  mg?: string;

  @Expose()
  @ApiProperty()
  p?: string;

  @Expose()
  @ApiProperty()
  k?: string;

  @Expose()
  @ApiProperty()
  na?: string;

  @Expose()
  @ApiProperty()
  zn?: string;

  @Expose()
  @ApiProperty()
  cu?: string;
}

class VitaminsDto {
  @Expose()
  @ApiProperty()
  vitaminA?: string;

  @Expose()
  @ApiProperty()
  retinol?: string;

  @Expose()
  @ApiProperty()
  betaCarotene?: string;

  @Expose()
  @ApiProperty()
  vitaminD?: string;

  @Expose()
  @ApiProperty()
  vitaminE?: string;

  @Expose()
  @ApiProperty()
  thiamin?: string;

  @Expose()
  @ApiProperty()
  riboflavin?: string;

  @Expose()
  @ApiProperty()
  niacin?: string;

  @Expose()
  @ApiProperty()
  vitaminB6?: string;

  @Expose()
  @ApiProperty()
  folate?: string;

  @Expose()
  @ApiProperty()
  vitaminC?: string;
}

export class GetFoodResponseDto {
  @Expose()
  @ApiProperty()
  id: string;

  @Expose()
  @ApiProperty()
  foodNameEnglish: string;

  @Expose()
  @ApiProperty()
  foodNameBengali: string;

  @Expose()
  @ApiProperty()
  image: string;

  @Expose()
  @ApiProperty()
  calories: string;

  @Expose()
  @ApiProperty()
  protein: string;

  @Expose()
  @ApiProperty()
  fat: string;

  @Expose()
  @ApiProperty()
  carb: string;

  @Expose()
  @ApiProperty({ type: MineralsDto })
  @Type(() => MineralsDto)
  minerals: MineralsDto;

  @Expose()
  @ApiProperty({ type: VitaminsDto })
  @Type(() => VitaminsDto)
  vitamins: VitaminsDto;

  @Expose()
  @ApiProperty()
  createdAt?: Date;

  @Expose()
  @ApiProperty()
  updatedAt?: Date;
}

export class GetSuccessFoodResponseDtoForAdmin
  implements ServiceSuccessResponse
{
  @ApiProperty({ type: GetFoodResponseDto })
  data: GetFoodResponseDto;
}

export class GetAllFoodSuccessResponseDto implements ServiceSuccessResponse {
  @Expose()
  @ApiProperty({ required: true, type: [GetFoodResponseDto] })
  @IsObject()
  @IsNotEmpty()
  data: [GetFoodResponseDto];
}
