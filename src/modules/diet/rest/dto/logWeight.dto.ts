import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsNumber, IsOptional, IsString } from 'class-validator';

export class LogWeightRequestDto {
  @ApiProperty({ description: 'Weight value', example: 75.5 })
  @IsNumber()
  weight: number;

  @ApiProperty({
    description: 'Weight unit',
    enum: ['kg', 'lb'],
    default: 'kg',
    example: 'kg',
  })
  @IsString()
  @IsOptional()
  weightType?: string = 'kg';

  @ApiProperty({
    description: 'Date of weight measurement',
    example: '2025-08-06T00:00:00.000Z',
    required: false,
  })
  @IsDateString()
  @IsOptional()
  date?: string;

  @ApiProperty({
    description: 'Optional notes',
    example: 'After workout',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}

export class LogWeightSuccessResponseDto {
  @ApiProperty()
  data: {
    id: string;
    userId: string;
    weight: number;
    weightType: string;
    date: string;
    notes: string;
    createdAt: string;
  };
}
