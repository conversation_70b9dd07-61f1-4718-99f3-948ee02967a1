import { ApiProperty } from '@nestjs/swagger';

export class GetWeightProgressSuccessResponseDto {
  @ApiProperty()
  data: {
    currentWeight: number;
    startingWeight: number;
    targetWeight: number;
    weightType: string;
    goalDisplay: {
      type: 'lose' | 'gain';
      amount: number;
      unit: string;
      weeksRemaining: number;
      estimatedEndDate: string;
    };
    progress: {
      totalWeightToChange: number;
      actualWeightChange: number;
      weightRemaining: number;
      progressPercentage: number;
      isGoalReached: boolean;
    };
    trend: {
      direction: 'losing' | 'gaining' | 'maintaining';
      averageWeeklyChange: number;
      averageDailyChange: number;
      totalDays: number;
      estimatedTimeToGoal: number;
      consistency: number;
    };
    visualization: {
      chartData: Array<{
        day: number;
        date: string;
        weight: number;
        change: number;
      }>;
      timeline: Array<{
        day: number;
        date: string;
        weight: number;
        change: number;
        source: string;
        trend: 'up' | 'down' | 'stable';
      }>;
      goalLine: {
        value: number;
        label: string;
      };
    };
  };
}
