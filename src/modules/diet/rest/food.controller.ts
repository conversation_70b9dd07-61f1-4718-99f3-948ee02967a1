import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { FoodService } from '../services/food.service';
import {
  CreateFoodDto,
  CreateSuccessFoodResponseDtoForAdmin,
} from './dto/food/createFood.dto';
import { DeleteFoodSuccessResponseDto } from './dto/food/deleteFood.dto';
import {
  GetAllFoodSuccessResponseDto,
  GetSuccessFoodResponseDtoForAdmin,
} from './dto/food/food.dto';
import {
  UpdateFoodDto,
  UpdateSuccessFoodResponseDtoForAdmin,
} from './dto/food/updateFood.dto';
import { sharedConfig } from 'config/shared';

@ApiTags('Food API - Admin')
@UseGuards(new RolesGuard(['admin']))
@ApiBearerAuth()
@Controller('admin/foods')
export class FoodController {
  constructor(private readonly foodService: FoodService) {}

  @ApiResponse({
    description: 'Create a Food Response',
    type: CreateSuccessFoodResponseDtoForAdmin,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({ summary: 'Create a new food' })
  @Post()
  async createFood(
    @Body() createFoodDto: CreateFoodDto,
  ): Promise<CreateSuccessFoodResponseDtoForAdmin> {
    return await this.foodService.createFood(createFoodDto);
  }

  @ApiResponse({
    description: 'Update a Food Response',
    type: UpdateSuccessFoodResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Update an existing food' })
  @Patch(':id')
  async updateFood(
    @Param('id') id: string,
    @Body() updateFoodDto: UpdateFoodDto,
  ): Promise<UpdateSuccessFoodResponseDtoForAdmin> {
    return await this.foodService.updateFood(id, updateFoodDto);
  }

  @ApiResponse({
    description: 'Get Food by ID Response',
    type: GetSuccessFoodResponseDtoForAdmin,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get Food by ID' })
  @Get(':id')
  async getFoodById(
    @Param('id') id: string,
  ): Promise<GetSuccessFoodResponseDtoForAdmin> {
    return await this.foodService.getFoodById(id);
  }

  @ApiResponse({
    description: 'Delete Food by ID Response',
    type: DeleteFoodSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Delete Food by ID' })
  @Delete(':id')
  async deleteFood(
    @Param('id') id: string,
  ): Promise<DeleteFoodSuccessResponseDto> {
    return await this.foodService.deleteFood(id);
  }

  @ApiResponse({
    description: 'Get all foods',
    type: GetAllFoodSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiQuery({ name: 'offset', type: Number, example: 0, required: false })
   @ApiQuery({ name: 'limit', type: Number, example: 10, required: false })
  @ApiOperation({ summary: 'Get all foods' })
  @Get()
  async getAllFoods(
     @Query('offset') offset = sharedConfig.defaultSkip,
    @Query('limit') limit = sharedConfig.defaultLimit,
  ): Promise<GetAllFoodSuccessResponseDto> {
    return await this.foodService.getAllFoods(offset, limit);
  }
}
