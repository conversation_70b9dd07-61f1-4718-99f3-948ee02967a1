import { BadRequestException, Injectable } from '@nestjs/common';
import { FoodRepository } from '../repositories/food.repository';

import { Helper } from 'src/helper/helper.interface';
import { deepCasting } from 'src/internal/casting/object.casting';
import { throwNotFoundErr } from 'src/internal/exception/api.exception.ext';
import { ERR_FOOD_NOT_FOUND } from '../const/food.constant';
import {
  CreateFoodDto,
  CreateFoodResponseDto,
  CreateSuccessFoodResponseDtoForAdmin,
} from '../rest/dto/food/createFood.dto';
import { DeleteFoodSuccessResponseDto } from '../rest/dto/food/deleteFood.dto';
import {
  GetAllFoodSuccessResponseDto,
  GetFoodResponseDto,
  GetSuccessFoodResponseDtoForAdmin,
} from '../rest/dto/food/food.dto';
import {
  UpdateFoodDto,
  UpdateSuccessFoodResponseDtoForAdmin,
} from '../rest/dto/food/updateFood.dto';

@Injectable()
export class FoodService {
  constructor(
    private readonly foodRepository: FoodRepository,
    private readonly helper: Helper,
  ) {}

  async createFood(
    createFoodDto: CreateFoodDto,
  ): Promise<CreateSuccessFoodResponseDtoForAdmin> {
    try {
      const food = await this.foodRepository.createFood(createFoodDto);
      const responseDto = deepCasting(CreateFoodResponseDto, food);
      return this.helper.serviceResponse.successResponse(responseDto);
    } catch (error) {
      throw new BadRequestException('Failed to create food');
    }
  }

  async updateFood(
    id: string,
    updateFoodDto: UpdateFoodDto,
  ): Promise<UpdateSuccessFoodResponseDtoForAdmin> {
    const food = await this.foodRepository.updateFood(id, updateFoodDto);
    throwNotFoundErr(!food, "Couldn't find the food", ERR_FOOD_NOT_FOUND);
    const responseDto = deepCasting(UpdateFoodDto, food);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getFoodById(id: string): Promise<GetSuccessFoodResponseDtoForAdmin> {
    const food = await this.foodRepository.getFoodById(id);
    throwNotFoundErr(!food, "Couldn't find the food", ERR_FOOD_NOT_FOUND);
    const responseDto = deepCasting(GetFoodResponseDto, food);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async deleteFood(id: string): Promise<DeleteFoodSuccessResponseDto> {
    const food = await this.foodRepository.deleteFood(id);
    throwNotFoundErr(!food, "Couldn't find the food", ERR_FOOD_NOT_FOUND);
    return this.helper.serviceResponse.successResponse({
      message: 'Food deleted successfully',
    });
  }

  async getAllFoods(
    offset: number,
    limit: number,
  ): Promise<GetAllFoodSuccessResponseDto> {
    const foods = await this.foodRepository.getAllFoods(offset, limit);

    const responseDto = foods.map((item) =>
      deepCasting(GetFoodResponseDto, item),
    );
    return this.helper.serviceResponse.successResponse(responseDto);
  }
}
